# ERPNext 知识库

[![VitePress](https://img.shields.io/badge/VitePress-1.6.3-brightgreen.svg)](https://vitepress.dev/)
[![Mermaid](https://img.shields.io/badge/Mermaid-11.6.0-blue.svg)](https://mermaid.js.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个面向业务人员的 ERPNext 实施指南和最佳实践知识库，助力企业数字化转型，提升管理效率。

## 📖 项目简介

本项目是一个基于 VitePress 构建的 ERPNext 知识库，旨在为企业管理者、业务人员和实施顾问提供全面的 ERPNext 学习和实施指导。内容涵盖从基础概念到高级应用的各个方面。

## 🚀 快速开始

### 环境要求

- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器

### 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 本地开发

```bash
# 启动开发服务器
npm run docs:dev

# 或使用 yarn
yarn docs:dev
```

访问 `http://localhost:5173` 查看文档。

### 构建部署

```bash
# 构建生产版本
npm run docs:build

# 预览构建结果
npm run docs:preview
```

## 📁 项目结构

```
erpnext_docs/
├── docs/                    # 文档相关配置和示例
├── faq/                     # 常见问题解答
│   ├── batch-management.md  # 批次管理问题
│   ├── mermaid-demo.md     # Mermaid 图表示例
│   └── order-save-and-submit.md # 订单保存提交问题
├── implementation-guide/    # 实施指南
│   ├── data-migration.md   # 数据迁移
│   ├── go-live-support.md  # 上线支持
│   ├── project-preparation.md # 项目准备
│   ├── system-configuration.md # 系统配置
│   └── user-training.md    # 用户培训
├── modules/                 # 模块说明
│   ├── accounting.md       # 财务会计
│   ├── crm.md             # 客户关系管理
│   ├── hr.md              # 人力资源
│   ├── inventory.md       # 库存管理
│   ├── manufacturing.md   # 生产制造
│   ├── procurement.md     # 采购管理
│   ├── projects.md        # 项目管理
│   └── sales.md           # 销售管理
├── quick-start/            # 快速入门
│   ├── daily-operation-guide.md # 日常操作指南
│   ├── key-accounting-entries.md # 关键会计分录
│   └── what-is-erpnext.md # ERPNext 简介
├── package.json           # 项目配置
└── README.md             # 项目说明文档
```

## 📚 内容概览

### 🎯 快速入门
- **ERPNext 简介**: 了解 ERPNext 的基本概念和核心功能
- **日常操作指南**: 掌握系统的基本操作流程
- **关键会计分录**: 理解重要的财务处理逻辑

### 🛠️ 实施指南
- **项目准备**: 实施前的准备工作和规划
- **系统配置**: 详细的系统配置步骤
- **数据迁移**: 数据迁移的方法和注意事项
- **用户培训**: 用户培训的内容和方法
- **上线支持**: 系统上线后的支持和维护

### 🔧 模块说明
- **销售管理**: 客户管理、订单处理、发票管理
- **采购管理**: 供应商管理、采购流程、成本控制
- **库存管理**: 仓库管理、库存控制、批次跟踪
- **财务会计**: 总账管理、应收应付、财务报表
- **生产制造**: 生产计划、BOM管理、成本核算
- **人力资源**: 员工管理、考勤薪资、绩效评估
- **项目管理**: 项目计划、任务跟踪、成本分析
- **客户关系管理**: 客户维护、销售机会、服务管理

### ❓ 常见问题
- **批次管理**: 批次号的设置和使用
- **订单处理**: 订单保存和提交的常见问题
- **系统配置**: 配置过程中的疑难解答

## 🎨 特色功能

### Mermaid 图表支持
本项目集成了 Mermaid 图表功能，可以绘制各种业务流程图、组织架构图等：

```mermaid
graph TD
    A[销售订单] --> B[库存检查]
    B --> C{库存充足?}
    C -->|是| D[发货]
    C -->|否| E[采购]
    E --> F[入库]
    F --> D
    D --> G[开票]
```

### 响应式设计
- 支持桌面端、平板和移动设备
- 自适应布局，优化阅读体验
- 深色/浅色主题切换

### 搜索功能
- 全文搜索支持
- 快速定位相关内容
- 智能搜索建议

## 🤝 贡献指南

我们欢迎社区贡献！如果您想为项目做出贡献，请遵循以下步骤：

1. Fork 本仓库
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

### 内容贡献
- 修正错误和改进现有内容
- 添加新的使用案例和最佳实践
- 翻译内容到其他语言
- 改进文档结构和导航

### 技术贡献
- 优化构建流程
- 改进用户界面
- 添加新功能
- 修复 bug

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [ERPNext 官网](https://erpnext.com/)
- [ERPNext GitHub](https://github.com/frappe/erpnext)
- [VitePress 文档](https://vitepress.dev/)
- [Mermaid 文档](https://mermaid.js.org/)

## 📞 联系我们

如果您有任何问题或建议，请通过以下方式联系我们：

- 提交 Issue
- 发送邮件
- 加入讨论群

---

**让我们一起构建更好的 ERPNext 知识库！** 🎉
